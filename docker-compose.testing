version: '2'

services:
    # IMPORTANT: not needed for Production deployments!
    # Used for testing via GitHub CI
    openldap_server:
        container_name: openldap_server
        # image: kiwitcms/openldap:2441-centos7-aarch64
        image: kiwitcms/openldap:2441-centos7-x86_64
        restart: always
        ports:
            - 389:389
            - 636:636

    db:
        container_name: db
        image: postgres:latest
        # IMPORTANT: ^^^ for Production deployments pin to a specific version number, see
        # https://hub.docker.com/_/postgres
        restart: always
        volumes:
            - db_data:/var/lib/postgresql/data
        environment:
            POSTGRES_DB: dummy_db
            POSTGRES_USER: dummy_usr
            POSTGRES_PASSWORD: dummy_s3cr3t

    web:
        container_name: web
        image: hub.kiwitcms.eu/kiwitcms/enterprise:latest
        # IMPORTANT: ^^^ for Production deployments pin to a specific version number, see
        # https://kiwitcms.org/containers/
        depends_on:
            - db
            - openldap_server
            # IMPORTANT: ^^^ not needed for Production deployments!
            # only used for testing via GitHub CI
        restart: always
        ports:
            - 80:8080
            - 443:8443
        volumes:
            - ./test_settings.py:/venv/lib64/python3.11/site-packages/tcms_settings_dir/test_settings.py:z,ro
            # IMPORTANT: ^^^ Remove or substitute your own settings overrides for Production deployments, see
            # https://kiwitcms.readthedocs.io/en/latest/installing_docker.html#customization
            - kiwi_ssl:/Kiwi/ssl:Z
            - kiwi_uploads:/Kiwi/uploads:Z
            - kiwi_letsencrypt:/etc/letsencrypt:Z
        environment:
            NGX_UPLOADS_RATE: 55
            NGX_UPLOADS_BURST: 20
            NGX_CSP_SCRIPT_SRC: cdn.example.bg
            # IMPORTANT: ^^^ Remove or substitute your own overrides for Production deployments, see
            # https://github.com/kiwitcms/enterprise#kiwi-tcms-enterprise-edition
            KIWI_TENANTS_DOMAIN: testing.example.bg
            DATABASE_URL: *****************************************/dummy_db
        # IMPORTANT: not needed for Production deployments!
        # Generated by testing/start_keycloak.sh
        env_file:
            - /tmp/kc.env

volumes:
    db_data:
    kiwi_ssl:
    kiwi_uploads:
    kiwi_letsencrypt:

# IMPORTANT: for testing purposes we use an external network!
# for Production deployments you will probably remove this entire section
# and let docker compose create the network automatically
networks:
  default:
    name: enterprise_default
    external: true
