# Copyright (c) 2024-2025 <PERSON> <<EMAIL>>
#
# Licensed under GNU Affero General Public License v3 or later (AGPLv3+)
# https://www.gnu.org/licenses/agpl-3.0.html

daemon on;
worker_processes auto;
error_log /dev/stderr;
pid /tmp/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

env NGX_AUTHENTICATED_RATE;
env NGX_AUTHENTICATED_BURST;
env NGX_ERRORS_RATE;
env NGX_ERRORS_BURST;
env NGX_STATIC_RATE;
env NGX_STATIC_BURST;
env NGX_UPLOADS_RATE;
env NGX_UPLOADS_BURST;
env NGX_CSP_SCRIPT_SRC;

http {
    client_body_temp_path  /var/lib/nginx/tmp/client_body 1 2;
    fastcgi_temp_path      /var/lib/nginx/tmp/fastcgi 1 2;
    proxy_temp_path        /var/lib/nginx/tmp/proxy 1 2;
    scgi_temp_path         /var/lib/nginx/tmp/scgi 1 2;
    uwsgi_temp_path        /var/lib/nginx/tmp/uwsgi 1 2;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /dev/stdout  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    gzip on;
    gzip_disable "msie6";

    # note: this should be bigger than
    # FILE_UPLOAD_MAX_SIZE from Kiwi TCMS which defaults to 5M.
    client_max_body_size 10m;

    # limit URI size, see
    # https://github.com/kiwitcms/Kiwi/issues/1054
    large_client_header_buffers 4 10k;

    ssl_certificate     /Kiwi/ssl/localhost.crt;
    ssl_certificate_key /Kiwi/ssl/localhost.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers off;

    # default proxy settings
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;

    map $request_uri $limit_key {
        default "";
        ~^/accounts/ $binary_remote_addr;
    }
    limit_req_zone $limit_key zone=limit-for-login-pages:10m rate=10r/s;
    limit_req_status 429;

    # similar to limit_req_zone definitions but in Lua
    lua_shared_dict limit_storage_authenticated 10m;
    lua_shared_dict limit_storage_errors 10m;
    lua_shared_dict limit_storage_static 10m;
    lua_shared_dict limit_storage_uploads 10m;

    upstream kiwitcms {
        server unix:///tmp/kiwitcms.sock;
    }

    # WARNING: make sure these match tcms.core.middleware.ExtraHeadersMiddleware
    header_filter_by_lua_block {
        local extra_script_src = os.getenv("NGX_CSP_SCRIPT_SRC") or ""
        if extra_script_src ~= "" then
            extra_script_src = " " .. extra_script_src
        end
        ngx.header.Content_Security_Policy = "script-src 'self' cdn.crowdin.com *.ethicalads.io plausible.io" .. extra_script_src .. ";";
    }

    server {
        listen       8080;
        listen       [::]:8080;
        server_name  _;

        location / {
                return 301 https://$host$request_uri;
        }
    }

    server {
        listen       8443 ssl;
        listen       [::]:8443 ssl;
        server_name  _;

        location = /favicon.ico {
            alias /Kiwi/static/images/favicon.ico;
        }

        location = /robots.txt {
            alias /Kiwi/static/robots.txt;
        }

        location /.well-known/ {
            alias /Kiwi/static/.well-known/;
        }

        location /uploads/  {
            set $rate_limit_config_key "uploads";
            access_by_lua_file /Kiwi/etc/rate-limit.lua;

            # prevent browser from possibly interpreting untrusted files
            types        { }
            default_type text/plain;

            alias /Kiwi/uploads/;
        }

        location /static/ {
            set $rate_limit_config_key "static";
            access_by_lua_file /Kiwi/etc/rate-limit.lua;

            alias /Kiwi/static/;
        }

        location / {
            limit_req zone=limit-for-login-pages burst=2 nodelay;

            set $rate_limit_config_key "authenticated";
            access_by_lua_file /Kiwi/etc/rate-limit.lua;

            include     /etc/nginx/uwsgi_params;
            uwsgi_pass  kiwitcms;
            uwsgi_intercept_errors on;

            # redirect for rate limiting; responds with 429, skip 429 itself
            error_page 400 401 402 403 404 405 406 407 408 409 410 411 412 413 414 415 416 417 418 421 422 423 424 426 428 431 451 500 501 502 503 504 505 506 507 508 510 511 @error_handler;
        }

        location @error_handler {
            set $rate_limit_config_key "errors";
            access_by_lua_file /Kiwi/etc/rate-limit.lua;
        }
    }
}
