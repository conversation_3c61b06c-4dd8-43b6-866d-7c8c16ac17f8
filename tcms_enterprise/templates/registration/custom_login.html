{% extends "registration/login.html" %}

{% comment %}
Copyright (c) 2018-2020 <PERSON> <<EMAIL>>

Licensed under GNU Affero General Public License v3 or later (AGPLv3+)
https://www.gnu.org/licenses/agpl-3.0.html
{% endcomment %}

{% load i18n %}
{% load static %}
{% load tcms_tenants %}
{% load enterprise_tags %}

{% block custom_login %}
<div>
    <p>
        {% trans "or Continue With" %}
    </p>

    {% for backend in backends.backends %}
    <a href='{% tenant_url request "public" %}{% url "social:begin" backend|lower %}?next={% next_url request %}'>
        {% with "images/social_auth/backends/"|add:backend|lower|add:".png" as image_path %}
            <img src='{% static image_path %}' alt="{{ backend|title }} login" title="{{ backend|title }} login">
        {% endwith %}
    </a>
    &nbsp;
    {% endfor %}
</div>
{% endblock %}
