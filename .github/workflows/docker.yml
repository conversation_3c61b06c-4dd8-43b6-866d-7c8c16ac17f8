name: docker

on:
  push:
    tags:
      - v*

permissions: read-all

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]

    steps:
      - name: Docker version info
        run: |
          docker --version
          docker --help

          docker compose --version
          docker compose --help

      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Python dependencies
        run: |
          sudo apt-get update
          sudo apt-get install libkrb5-dev libxml2-dev libxmlsec1-dev libxmlsec1-openssl
          pip install -U pip setuptools wheel
          pip install -r devel.txt

      - name: Build & push docker images for ${{ github.event.ref }}
        run: |
            VERSION=$(make echo-version)

            echo "${{ secrets.QUAY_PUSH_TOKEN }}" | docker login -u="${{ secrets.QUAY_PUSH_USERNAME }}" --password-stdin quay.io
            echo "${{ secrets.QUAY_PUSH_TOKEN }}" | docker login -u="${{ secrets.QUAY_PUSH_USERNAME }}" --password-stdin hub.kiwitcms.eu

            make docker-image
            # retag for private container registry
            docker tag hub.kiwitcms.eu/kiwitcms/enterprise:$VERSION-$(uname -m) quay.io/kiwitcms/enterprise:$VERSION-$(uname -m)

            echo "+++++ Docker images +++++"
            docker images

            docker push quay.io/kiwitcms/enterprise:$VERSION-$(uname -m)
            docker logout quay.io
            docker logout hub.kiwitcms.eu
