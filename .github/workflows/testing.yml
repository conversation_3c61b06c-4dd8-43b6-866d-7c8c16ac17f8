name: testing

on:
  pull_request:
    branches:
      - "*"
  push:
    branches: master

permissions: read-all

jobs:
  test_matrix:
    name: ${{ matrix.command }} (${{ matrix.python-version}})
    runs-on: ubuntu-latest
    strategy:
      fail-fast: true
      matrix:
        python-version: [3.11]
        command: [flake8, pylint, test-docker-image]

    steps:
    - name: Docker version info
      run: |
        docker --version
        docker --help

        docker compose --version
        docker compose --help

    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Python dependencies
      run: |
        sudo apt-get update
        sudo apt-get install libkrb5-dev libxml2-dev libxmlsec1-dev libxmlsec1-openssl
        pip install -U pip setuptools wheel
        pip install -r devel.txt

    - name: Install packages used during container testing
      if: matrix.command == 'test-docker-image'
      run: |
          # remove stock FF package
          sudo snap remove firefox
          sudo apt-get remove firefox

          # used in test scripts
          sudo apt-get install git make ldap-utils wrk

          # install beakerlib from source it doesn't ship DEB packages
          if [ ! -f "/usr/share/beakerlib/beakerlib.sh" ]; then
              git clone https://github.com/beakerlib/beakerlib.git
              sudo make -C beakerlib/ install
          fi

          # install Firefox and Geckodriver from Mozilla's DEB repository
          # b/c Ubuntu 22.04 and later ships FF via snap package (a container)
          # which causes issues with file access from RobotFramework tests
          sudo apt-get install software-properties-common
          sudo add-apt-repository --yes ppa:mozillateam/ppa

          # prioritize the 3rd party repository
          sudo tee /etc/apt/preferences.d/mozilla-firefox << EOF
          Package: *
          Pin: release o=LP-PPA-mozillateam
          Pin-Priority: 1001

          Package: firefox
          Pin: version 1:1snap1-0ubuntu2
          Pin-Priority: -1
          EOF
          sudo apt-get install firefox firefox-geckodriver

    - name: Login to Private Container Registry
      if: matrix.command == 'test-docker-image'
      run: |
        echo "${{ secrets.QUAY_PUSH_TOKEN }}" | docker login -u="${{ secrets.QUAY_PUSH_USERNAME }}" --password-stdin hub.kiwitcms.eu

    - name: make ${{ matrix.command }}
      run: |
        make ${{ matrix.command }}

    - name: Logout of Private Container Registry
      if: ${{ always() && matrix.command == 'test-docker-image' }}
      run: |
        docker logout hub.kiwitcms.eu

    - name: Upload testing artifacts
      if: always() && matrix.command == 'test-docker-image'
      uses: actions/upload-artifact@v4
      with:
        name: testing-artifacts
        path: |
          ./*.json
          ./*.html
          ./*.log
          ./wrk-logs-*/
          ./*docker.log
          ./*cookies*
          ./test*.txt
