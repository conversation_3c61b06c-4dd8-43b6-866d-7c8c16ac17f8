name: lint

on:
  push:
    branches: master
  pull_request:

permissions: read-all

jobs:
  checkov:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Check with checkov
        run: |
          pip install checkov
          checkov --quiet --directory .
