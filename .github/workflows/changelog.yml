name: changelog

on:
  pull_request:
    paths:
      - 'setup.**'
      - 'README.**'

permissions: read-all

jobs:
  acceptance:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: check-source-branch-and-modified-files
      env:
        HEAD_REF: ${{ github.head_ref }}
      run: |
        echo "INFO: source branch is: $HEAD_REF"

        # if this PR modifies README.md or setup.py file it needs to come from a branch
        # that follows the pattern 'prepare/vX.Y' b/c we want to run some extra jobs for such branches!
        if [[ ! "$HEAD_REF" =~ ^prepare/v.+$ ]]; then
            echo "FAIL: Modifications to README.md or setup.py are only accepted from 'prepare/vX.Y' branches!"
            echo "INFO: Otherwise aarch64 jobs in Circle CI will not be executed."
            exit 1
        fi
