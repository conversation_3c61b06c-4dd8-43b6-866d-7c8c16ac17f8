# Copyright (c) 2021-2025 <PERSON> <<EMAIL>>
#
# Licensed under GNU Affero General Public License v3 or later (AGPLv3+)
# https://www.gnu.org/licenses/agpl-3.0.html

# checkov:skip=CKV_DOCKER_2:Ensure that HEALTHCHECK instructions have been added to container images
# checkov:skip=CKV_DOCKER_3:Ensure that a user for the container has been created
FROM quay.io/centos/centos:stream9

RUN sed -i "s/enabled=0/enabled=1/" /etc/yum.repos.d/centos.repo && \
    dnf -y --disablerepo="*-source" --disablerepo="*-debuginfo" install \
        gcc python3.11-devel libtool-ltdl-devel pkgconf-pkg-config \
        libxml2-devel libxslt-devel xmlsec1-devel xmlsec1-openssl-devel opensc softhsm openssl-pkcs11 && \
    dnf -y --disablerepo="*-source" --disablerepo="*-debuginfo" update

ENV PATH /venv/bin:${PATH} \
    VIRTUAL_ENV /venv

RUN python3.11 -m venv /venv
RUN pip3 install --no-cache-dir --upgrade pip wheel
# workaround for
# https://github.com/xmlsec/python-xmlsec/issues/344 and
# https://github.com/xmlsec/python-xmlsec/issues/336 and
# https://github.com/xmlsec/python-xmlsec/issues/314
RUN pip3 wheel --no-deps --wheel-dir /dist xmlsec==1.3.14
